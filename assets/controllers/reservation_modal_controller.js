import { Controller } from '@hotwired/stimulus';
import { initFlowbite } from 'flowbite';
import { Datepicker } from 'flowbite-datepicker';


export default class extends Controller {
    static values = { bookedDates: Array }

    connect() {
        this.form = this.element.querySelector('form');
        this.deskId = this.element.id.replace('reservationModal-', '');
        this.dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);

        if (this.form) {
            this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        } else {
            console.error('Form not found in modal');
        }

        // Initialize the datepicker after a short delay to ensure DOM is ready
        setTimeout(() => {
            this.initDatepicker();
        }, 100);
    }

    initDatepicker() {
        const datepickerEl = document.getElementById(`datepicker-inline-${this.deskId}`);
        if (!datepickerEl || !this.dateInput) {
            console.error('Datepicker element or date input not found');
            return;
        }

        // Clear any existing content
        datepickerEl.innerHTML = '';

        // Get availability data from the hidden input
        const availability = this.getAvailabilityFromInput();
        const disabledDays = this.getDisabledDaysOfWeek(availability);

        console.log('Booked dates:', this.bookedDatesValue);
        console.log('Disabled days:', disabledDays);

        // Convert booked dates to proper format for Flowbite
        const bookedDatesFormatted = (this.bookedDatesValue || []).map(date => {
            // Ensure the date is in the correct format
            return new Date(date + 'T00:00:00');
        });

        // Initialize Flowbite datepicker
        const datepicker = new Datepicker(datepickerEl, {
            format: 'yyyy-mm-dd',
            datesDisabled: bookedDatesFormatted,
            daysOfWeekDisabled: disabledDays,
            weekStart: 1,
            minDate: new Date(), // Disable past dates
            autohide: false,
            todayHighlight: true,
        });

        // Store datepicker instance for later use
        this.datepicker = datepicker;

        // Listen for date selection - Flowbite uses 'changeDate' event
        datepickerEl.addEventListener('changeDate', (event) => {
            console.log('Date change event:', event);
            if (event.detail && event.detail.date) {
                const selectedDate = event.detail.date;
                const formattedDate = this.formatDate(selectedDate);
                this.dateInput.value = formattedDate;
                console.log('Date selected and set:', formattedDate);
            }
        });

        // Also listen for click events on date cells as a fallback
        datepickerEl.addEventListener('click', (event) => {
            // Small delay to allow Flowbite to process the click
            setTimeout(() => {
                const selectedDateEl = datepickerEl.querySelector('.selected');
                if (selectedDateEl && selectedDateEl.dataset.date) {
                    const dateStr = selectedDateEl.dataset.date;
                    this.dateInput.value = dateStr;
                    console.log('Date selected via click:', dateStr);
                }
            }, 100);
        });
    }

    getAvailabilityFromInput() {
        if (!this.dateInput) return {};

        return {
            monday: this.dateInput.dataset.monday === '1',
            tuesday: this.dateInput.dataset.tuesday === '1',
            wednesday: this.dateInput.dataset.wednesday === '1',
            thursday: this.dateInput.dataset.thursday === '1',
            friday: this.dateInput.dataset.friday === '1',
            saturday: this.dateInput.dataset.saturday === '1',
            sunday: this.dateInput.dataset.sunday === '1',
        };
    }

    getDisabledDaysOfWeek(availability) {
        const disabledDays = [];
        const dayMap = {
            sunday: 0,
            monday: 1,
            tuesday: 2,
            wednesday: 3,
            thursday: 4,
            friday: 5,
            saturday: 6
        };

        Object.keys(availability).forEach(day => {
            if (!availability[day]) {
                disabledDays.push(dayMap[day]);
            }
        });

        return disabledDays;
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    handleFormSubmit(event) {
        // Validate that a date is selected
        if (!this.dateInput || !this.dateInput.value) {
            event.preventDefault();
            console.error('Please select a date for your reservation');
            alert('Veuillez sélectionner une date pour votre réservation');
            return false;
        }

        console.log('Form submitted with date:', this.dateInput.value);
        return true;
    }

}
