import { Controller } from '@hotwired/stimulus';


export default class extends Controller {
    static values = { bookedDates: Array }

    connect() {
        this.form = this.element.querySelector('form');
        this.deskId = this.element.id.replace('reservationModal-', '');
        this.dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);

        if (this.form) {
            this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        } else {
            console.error('Form not found in modal');
        }

        // Initialize the datepicker after a short delay to ensure DOM is ready
        setTimeout(() => {
            this.initDatepicker();
        }, 100);
    }

    initDatepicker() {
        const datepickerEl = document.getElementById(`datepicker-inline-${this.deskId}`);
        if (!datepickerEl || !this.dateInput) {
            console.error('Datepicker element or date input not found');
            return;
        }

        // Clear any existing content
        datepickerEl.innerHTML = '';

        // Get availability data from the hidden input
        const availability = this.getAvailabilityFromInput();
        const disabledDays = this.getDisabledDaysOfWeek(availability);

        // Get booked dates from the component value or global window object
        const bookedDates = this.bookedDatesValue || window.bookedDates?.[this.deskId] || [];

        console.log('Initializing datepicker for desk:', this.deskId);
        console.log('Booked dates:', bookedDates);
        console.log('Disabled days:', disabledDays);

        // Function to check if a date should be disabled
        const isDateDisabled = (date) => {
            // Create today's date without timezone issues
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // Format date as YYYY-MM-DD without timezone conversion
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const dateStr = `${year}-${month}-${day}`;

            const isPast = date < today;
            const isBooked = bookedDates.includes(dateStr);
            const isUnavailableDay = disabledDays.includes(date.getDay());

            return isPast || isBooked || isUnavailableDay;
        };

        // Initialize Flatpickr
        if (typeof flatpickr !== 'undefined') {
            this.flatpickrInstance = flatpickr(datepickerEl, {
                dateFormat: 'Y-m-d',
                minDate: 'today',
                inline: true,
                static: true,
                disableMobile: true,
                enableTime: false,
                disable: [isDateDisabled],
                onChange: (selectedDates, dateStr, instance) => {
                    // Ensure we use the dateStr directly to avoid timezone issues
                    this.dateInput.value = dateStr;
                    console.log('Date selected:', dateStr);
                }
            });
        } else {
            console.error('Flatpickr is not loaded');
        }
    }

    getAvailabilityFromInput() {
        if (!this.dateInput) return {};

        return {
            monday: this.dateInput.dataset.monday === '1',
            tuesday: this.dateInput.dataset.tuesday === '1',
            wednesday: this.dateInput.dataset.wednesday === '1',
            thursday: this.dateInput.dataset.thursday === '1',
            friday: this.dateInput.dataset.friday === '1',
            saturday: this.dateInput.dataset.saturday === '1',
            sunday: this.dateInput.dataset.sunday === '1',
        };
    }

    getDisabledDaysOfWeek(availability) {
        const disabledDays = [];
        const dayMap = {
            sunday: 0,
            monday: 1,
            tuesday: 2,
            wednesday: 3,
            thursday: 4,
            friday: 5,
            saturday: 6
        };

        Object.keys(availability).forEach(day => {
            if (!availability[day]) {
                disabledDays.push(dayMap[day]);
            }
        });

        return disabledDays;
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    handleFormSubmit(event) {
        // Validate that a date is selected
        if (!this.dateInput || !this.dateInput.value) {
            event.preventDefault();
            console.error('Please select a date for your reservation');
            alert('Veuillez sélectionner une date pour votre réservation');
            return false;
        }

        console.log('Form submitted with date:', this.dateInput.value);
        return true;
    }

}
