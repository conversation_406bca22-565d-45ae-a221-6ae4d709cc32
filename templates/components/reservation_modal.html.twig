<div id="reservationModal-{{ desk.id }}" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 bottom-0 flex items-center justify-center z-50 hidden overflow-y-auto overflow-x-hidden" data-controller="reservation-modal" data-reservation-modal-booked-dates-value="{{ bookedDates|json_encode }}">
        <div class="bg-white/50 dark:bg-gray-900/50 absolute inset-0"></div>
        <div class="relative w-full max-w-md z-10 max-h-[90vh] overflow-y-auto">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Réserver {{ desk.name }}
                    </h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center" data-modal-hide="reservationModal-{{ desk.id }}">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 space-y-4">
                    <form action="{{ path('app_reservation_new', {'id': desk.id}) }}"
                          method="post"
                          id="reservation-form-{{ desk.id }}"
                          data-turbo="false"
                          target="_top">
                        <input type="hidden" name="_token" value="{{ csrf_token('reservation_form') }}">
                        <input type="hidden" name="reservation_form[_token]" value="{{ csrf_token('reservation_form') }}">
                        <input type="hidden" name="desk_id" value="{{ desk.id }}">
                        <input type="hidden" name="turbo" value="1">
                        <div class="mb-4">
                            <label for="reservation_date_{{ desk.id }}" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Sélectionnez une date</label>
                            <input type="hidden" id="reservation_date_{{ desk.id }}" name="reservation_form[reservationDate]"
                                   data-monday="{{ desk.space.availability.isMonday ? '1' : '0' }}"
                                   data-tuesday="{{ desk.space.availability.isTuesday ? '1' : '0' }}"
                                   data-wednesday="{{ desk.space.availability.isWednesday ? '1' : '0' }}"
                                   data-thursday="{{ desk.space.availability.isThursday ? '1' : '0' }}"
                                   data-friday="{{ desk.space.availability.isFriday ? '1' : '0' }}"
                                   data-saturday="{{ desk.space.availability.isSaturday ? '1' : '0' }}"
                                   data-sunday="{{ desk.space.availability.isSunday ? '1' : '0' }}"
                                   required>
                            <div id="datepicker-inline-{{ desk.id }}" inline-datepicker></div>
                        </div>
                        <div class="flex justify-end items-center pt-4 pb-3 space-x-3 border-t border-gray-200 rounded-b dark:border-gray-600">
                            <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 rounded-lg border text-sm font-medium px-5 py-2.5" data-modal-hide="reservationModal-{{ desk.id }}">Annuler</button>
                            <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 font-medium rounded-lg text-sm px-5 py-2.5">Confirmer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>